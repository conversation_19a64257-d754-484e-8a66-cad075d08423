/* CommonJS version of generate-featured-from-eras */
const fs = require('fs');
const path = require('path');

const ERA_DIR = path.join(process.cwd(), 'public', 'data', 'artists', 'ye', 'categories', 'unreleased', 'eras');
const OUTPUT = path.join(process.cwd(), 'public', 'data', 'featured-tracks.json');

function readJSON(p) {
  return JSON.parse(fs.readFileSync(p, 'utf-8'));
}

function safeParseJSON(str) {
  try { return JSON.parse(str); } catch { return null; }
}

function sample(array, n) {
  const arr = array.slice();
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr.slice(0, n);
}

function extractUrlFromOriginalContent(oc) {
  if (!oc) return { url: undefined, ocId: undefined };
  const data = typeof oc === 'string' ? safeParseJSON(oc) : oc;
  if (data && typeof data === 'object') {
    const url = data.url || (data.id ? `https://api.pillowcase.su/api/download/${data.id}` : undefined);
    return { url, ocId: data.id };
  }
  return { url: undefined, ocId: undefined };
}

function toFeaturedItems(eraJson) {
  const cover = eraJson.coverImage || '/images/eras/album-placeholder.svg';
  const eraName = eraJson.name || eraJson.id || 'Unreleased';
  const tracks = Array.isArray(eraJson.tracks) ? eraJson.tracks : [];

  const candidates = tracks.filter(t => {
    if (t.originalUrl) return true;
    const { url } = extractUrlFromOriginalContent(t.originalContent);
    return Boolean(url);
  });

  const picks = sample(candidates, Math.min(2, candidates.length));

  return picks.map(t => {
    const { url, ocId } = extractUrlFromOriginalContent(t.originalContent);
    const id = ocId || t.id || (url ? url.split('/').pop() : undefined) || `${eraJson.id}-${Math.random().toString(36).slice(2, 8)}`;
    const artists = Array.isArray(t.artists) ? t.artists.join(', ') : (t.artists || 'Ye');

    return {
      id,
      title: t.name || t.title || 'Untitled',
      artists,
      albumCover: cover,
      category: 'unreleased',
      era: eraName,
      description: t.notes || '',
      url: url || '',
      originalUrl: t.originalUrl || ''
    };
  });
}

function main() {
  if (!fs.existsSync(ERA_DIR)) {
    console.error('Era directory not found:', ERA_DIR);
    process.exit(1);
  }
  const files = fs.readdirSync(ERA_DIR).filter(f => f.endsWith('.json'));
  const allItems = [];
  for (const file of files) {
    const eraPath = path.join(ERA_DIR, file);
    try {
      const eraJson = readJSON(eraPath);
      const items = toFeaturedItems(eraJson);
      allItems.push(...items);
    } catch (e) {
      console.warn('Skip invalid era file:', file, e.message);
    }
  }

  const sorted = allItems.sort((a, b) => (a.era || '').localeCompare(b.era || ''));
  fs.writeFileSync(OUTPUT, JSON.stringify(sorted, null, 2));
  console.log(`✅ Generated ${sorted.length} featured tracks to ${OUTPUT}`);
}

main();

