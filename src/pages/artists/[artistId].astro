---
import MainLayout from '../../layouts/MainLayout.astro';
import artistsData from '../../data/artists.json';
import { t } from '../../i18n';
import SimpleSubmissionForm from '../../components/content-submission/SimpleSubmissionForm.astro';
import { ENABLE_SUBMISSION_FORM } from '../../config/featureFlags';

// 获取当前语言
const lang = Astro.url ? t('', null, Astro.url.toString()) : 'en';

// 启用预渲染
export const prerender = true;

export async function getStaticPaths() {
  return artistsData.artists.map(artist => ({
    params: { artistId: artist.id },
    props: { artist }
  }));
}

const { artistId } = Astro.params;
const { artist } = Astro.props;

// 确保 artist 存在
if (!artist) {
  throw new Error(`Artist with ID ${artistId} not found`);
}

// 面包屑导航数据
const breadcrumbs = [
  { name: 'Home', url: '/' },
  { name: 'Artists', url: '/artists' },
  { name: artist.name, url: `/artists/${artist.id}`, current: true }
];
---

<MainLayout title={`${artist.name} - TrackerHive`}>
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbs.map((breadcrumb, index) => (
          <li class="inline-flex items-center">
            {index > 0 && (
              <svg class="w-3 h-3 mx-1 text-text-secondary" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
              </svg>
            )}
            <a 
              href={breadcrumb.url} 
              class={`inline-flex items-center text-sm font-medium ${
                breadcrumb.current 
                  ? 'text-primary cursor-default' 
                  : 'text-text-secondary hover:text-primary'
              }`}
              aria-current={breadcrumb.current ? 'page' : undefined}
            >
              {breadcrumb.name}
            </a>
          </li>
        ))}
      </ol>
    </nav>
    
    <!-- 艺术家头部 - 移动端优化版本 -->
    <div class="mb-10 rounded-xl overflow-hidden shadow-lg bg-dark-elevated">
      <!-- 移动端布局：图片在上，文字在下 -->
      <div class="md:hidden flex flex-col">
        <div class="w-full h-64 overflow-hidden bg-dark">
          {artist.image ? (
            <>
              <img 
                src={artist.image} 
                alt={`${artist.name} - American rapper, producer, and fashion designer`} 
                class="w-full h-full object-cover object-center" 
                onerror="this.onerror=null; this.style.display='none'; this.parentNode.querySelector('.artist-placeholder').style.display='flex';"
                loading="eager"
              />
              <div class="artist-placeholder w-full h-full hidden items-center justify-center bg-dark text-text-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M5.52 19.346a7.704 7.704 0 0 0 6.48 1.154 7.706 7.706 0 0 0 6.48-1.154"/>
                  <circle cx="12" cy="10" r="3"/>
                </svg>
              </div>
            </>
          ) : (
            <div class="w-full h-full flex items-center justify-center bg-dark text-text-secondary">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M5.52 19.346a7.704 7.704 0 0 0 6.48 1.154 7.706 7.706 0 0 0 6.48-1.154"/>
                <circle cx="12" cy="10" r="3"/>
              </svg>
            </div>
          )}
        </div>
        <div class="px-6 py-6 bg-dark-elevated">
          <h1 class="text-2xl font-bold mb-3 text-white">{artist.name}</h1>
          {artist.aliases && artist.aliases.length > 0 && (
            <p class="text-sm text-text-secondary mb-3">
              {t('artist.alsoKnownAs', lang)}: {artist.aliases.join(', ')}
            </p>
          )}
          <p class="text-sm text-text-secondary mb-4">
            {artist.id === 'ye' ? t('artist.yeDescription', lang) :
             artist.id === 'playboi-carti' ? t('artist.playboiCartiDescription', lang) :
             artist.description}
          </p>


        </div>
      </div>
      
      <!-- 桌面端布局：图片在右，文字在左 -->
      <div class="hidden md:block relative">
        <div class="absolute top-0 right-0 w-1/3 lg:w-1/3 h-full overflow-hidden bg-dark">
          {artist.image ? (
            <>
              <img 
                src={artist.image} 
                alt={`${artist.name} - American rapper, producer, and fashion designer`} 
                class="w-full h-full object-cover" 
                onerror="this.onerror=null; this.style.display='none'; this.parentNode.querySelector('.artist-placeholder').style.display='flex';"
                loading="eager"
              />
              <div class="artist-placeholder w-full h-full hidden items-center justify-center bg-dark text-text-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M5.52 19.346a7.704 7.704 0 0 0 6.48 1.154 7.706 7.706 0 0 0 6.48-1.154"/>
                  <circle cx="12" cy="10" r="3"/>
                </svg>
              </div>
            </>
          ) : (
            <div class="w-full h-full flex items-center justify-center bg-dark text-text-secondary">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M5.52 19.346a7.704 7.704 0 0 0 6.48 1.154 7.706 7.706 0 0 0 6.48-1.154"/>
                <circle cx="12" cy="10" r="3"/>
              </svg>
            </div>
          )}
        </div>
        <div class="px-8 py-16 relative z-10">
          <div class="max-w-2xl">
            <h1 class="text-4xl font-bold mb-4 text-white">{artist.name}</h1>
            {artist.aliases && artist.aliases.length > 0 && (
              <p class="text-base text-text-secondary mb-4">
                {t('artist.alsoKnownAs', lang)}: {artist.aliases.join(', ')}
              </p>
            )}
            <p class="text-lg text-text-secondary mb-6">
              {artist.id === 'ye' ? t('artist.yeDescription', lang) :
             artist.id === 'playboi-carti' ? t('artist.playboiCartiDescription', lang) :
             artist.description}
            </p>


          </div>
        </div>
      </div>
    </div>

    <!-- Artist Introduction Section -->
    {artist.id === 'ye' && (
      <section class="mb-8">
        <div class="bg-dark-elevated rounded-xl p-6 border border-dark-hover">
          <h2 class="text-xl font-bold text-white mb-4">Welcome to the Ultimate Music Experience</h2>
          <p class="text-gray-300 mb-4">
            Discover the most comprehensive platform dedicated to Kanye West's musical journey. Our system provides unparalleled access to unreleased content, rare demos, and exclusive music that you won't find anywhere else.
          </p>
          <p class="text-gray-300">
            From classic albums to unreleased gems, our ye tracker offers the most complete collection for true Ye fans. Explore ye tracker categories, discover new content, and stay updated with the latest additions.
          </p>
        </div>
      </section>
    )}

    {artist.id === 'playboi-carti' && (
      <section class="mb-8">
        <div class="bg-dark-elevated rounded-xl p-6 border border-dark-hover">
          <h2 class="text-xl font-bold text-white mb-4">Welcome to the Ultimate Music Experience</h2>
          <p class="text-gray-300 mb-4">
            Discover the most comprehensive carti tracker platform dedicated to musical evolution. Our carti tracker system provides unparalleled access to exclusive content, rare demos, and unreleased music from the enigmatic artist.
          </p>
          <p class="text-gray-300">
            From experimental sounds to gothic aesthetics, our carti tracker offers the most complete collection for carti tracker enthusiasts. Explore carti tracker categories, discover new releases, and stay updated with the latest additions.
          </p>
        </div>
      </section>
    )}

    <!-- Artist Features Section -->
    {artist.id === 'ye' && (
      <section class="mb-12">
        <div class="bg-dark-elevated rounded-xl p-6 border border-dark-hover">
          <h2 class="text-xl font-bold text-white mb-6">Why Choose Our Platform?</h2>
          <div class="grid md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h3 class="font-semibold text-white mb-1">Comprehensive Database</h3>
                  <p class="text-gray-400 text-sm">Access the largest collection of unreleased music with our advanced system.</p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h3 class="font-semibold text-white mb-1">Exclusive Content</h3>
                  <p class="text-gray-400 text-sm">Discover exclusive tracks, rare demos, and leaked music through our platform.</p>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h3 class="font-semibold text-white mb-1">Real-time Updates</h3>
                  <p class="text-gray-400 text-sm">Stay updated with the latest news and music discoveries.</p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h3 class="font-semibold text-white mb-1">Free Access</h3>
                  <p class="text-gray-400 text-sm">Enjoy unlimited access to our platform and content without any fees.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    )}

    {artist.id === 'playboi-carti' && (
      <section class="mb-12">
        <div class="bg-dark-elevated rounded-xl p-6 border border-dark-hover">
          <h2 class="text-xl font-bold text-white mb-6">Why Choose Our Platform?</h2>
          <div class="grid md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h3 class="font-semibold text-white mb-1">Comprehensive Database</h3>
                  <p class="text-gray-400 text-sm">Access the largest collection of carti tracker music with our advanced carti tracker system.</p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h3 class="font-semibold text-white mb-1">Exclusive Content</h3>
                  <p class="text-gray-400 text-sm">Discover exclusive tracks, rare demos, and leaked music through our platform.</p>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h3 class="font-semibold text-white mb-1">Real-time Updates</h3>
                  <p class="text-gray-400 text-sm">Stay updated with the latest news and music discoveries from our community.</p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <h3 class="font-semibold text-white mb-1">Free Access</h3>
                  <p class="text-gray-400 text-sm">Enjoy unlimited access to our platform and exclusive content without any fees.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    )}

    <!-- 分类列表 -->
    <section>
      {artist.id === 'ye' && (
        <h2 class="text-2xl font-bold text-white mb-6">Explore Categories</h2>
      )}
      {artist.id === 'playboi-carti' && (
        <h2 class="text-2xl font-bold text-white mb-6">Explore Categories</h2>
      )}
      {artist.id !== 'ye' && artist.id !== 'playboi-carti' && (
        <h2 class="text-2xl font-bold text-white mb-6">{t('artist.categories', lang)}</h2>
      )}
      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {artist.categories.map((category) => {
          // 检查分类是否有数据
          const hasData = category.hasData !== undefined ? category.hasData : true;
          return (
            <a href={`/artists/${artist.id}/${category.id}`} class="group">
              <div class={`bg-dark-elevated border border-dark-hover rounded-xl overflow-hidden transition-all hover:bg-dark-hover shadow-md hover:shadow-lg hover:border-primary/50 p-4 ${!hasData ? 'opacity-80' : ''}`}>
                <h3 class="text-lg font-semibold text-white text-center">{t(`categories.${category.id}`, lang) || category.name}</h3>
                {/* 根据记忆中的要求，移除分类描述，保持简洁设计 */}
              </div>
            </a>
          );
        })}
      </div>
    </section>

    <!-- Add Track Section (feature-flagged) -->
    {ENABLE_SUBMISSION_FORM && (
      <section class="mt-12">
        <SimpleSubmissionForm artistId={artist.id} />
      </section>
    )}

    <!-- FAQ Section for SEO -->
    {artist.id === 'ye' && (
      <section class="mt-16 mb-12">
        <div class="text-center mb-10">
          <h2 class="text-2xl font-bold text-white mb-4">
            Frequently Asked Questions
          </h2>
          <p class="text-gray-400 max-w-2xl mx-auto">
            Learn more about our platform and features.
          </p>
        </div>

        <div class="max-w-3xl mx-auto space-y-4">
          <!-- FAQ Item 1 -->
          <div class="bg-dark-elevated rounded-xl border border-dark-hover p-6">
            <h3 class="font-semibold text-white mb-2">What makes our platform special?</h3>
            <p class="text-gray-300 text-sm">
              Our platform provides the most comprehensive database with exclusive content and real-time updates.
            </p>
          </div>

          <!-- FAQ Item 2 -->
          <div class="bg-dark-elevated rounded-xl border border-dark-hover p-6">
            <h3 class="font-semibold text-white mb-2">How often is the database updated?</h3>
            <p class="text-gray-300 text-sm">
              Our system receives daily updates with new content and discoveries from the community.
            </p>
          </div>

          <!-- FAQ Item 3 -->
          <div class="bg-dark-elevated rounded-xl border border-dark-hover p-6">
            <h3 class="font-semibold text-white mb-2">Is the platform free to use?</h3>
            <p class="text-gray-300 text-sm">
              Yes, our platform is completely free, providing unlimited access to music and content.
            </p>
          </div>
        </div>
      </section>
    )}

    {artist.id === 'playboi-carti' && (
      <section class="mt-16 mb-12">
        <div class="text-center mb-10">
          <h2 class="text-2xl font-bold text-white mb-4">
            Frequently Asked Questions
          </h2>
          <p class="text-gray-400 max-w-2xl mx-auto">
            Learn more about our platform and exclusive features.
          </p>
        </div>

        <div class="max-w-3xl mx-auto space-y-4">
          <!-- FAQ Item 1 -->
          <div class="bg-dark-elevated rounded-xl border border-dark-hover p-6">
            <h3 class="font-semibold text-white mb-2">What makes our platform special?</h3>
            <p class="text-gray-300 text-sm">
              Our carti tracker provides the most comprehensive database with exclusive content and real-time carti tracker updates.
            </p>
          </div>

          <!-- FAQ Item 2 -->
          <div class="bg-dark-elevated rounded-xl border border-dark-hover p-6">
            <h3 class="font-semibold text-white mb-2">How often is the database updated?</h3>
            <p class="text-gray-300 text-sm">
              Our carti tracker receives daily updates with new discoveries and exclusive content from the carti tracker community.
            </p>
          </div>

          <!-- FAQ Item 3 -->
          <div class="bg-dark-elevated rounded-xl border border-dark-hover p-6">
            <h3 class="font-semibold text-white mb-2">Is the platform free to use?</h3>
            <p class="text-gray-300 text-sm">
              Yes, our carti tracker platform is completely free, providing unlimited access to exclusive carti tracker content and music.
            </p>
          </div>
        </div>
      </section>
    )}
  </main>
</MainLayout>


