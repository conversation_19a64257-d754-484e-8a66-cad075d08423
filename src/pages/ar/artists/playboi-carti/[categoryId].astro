---
import MainLayout from '../../../../layouts/MainLayout.astro';
import { promises as fs } from 'fs';
import path from 'path';
import SmartImage from '../../../../components/SmartImage.astro';
import { t } from '../../../../i18n/index.js';

import AlbumGridDisplay from '../../../../components/categoryViews/AlbumGridDisplay.astro';
import TrackListDisplay from '../../../../components/categoryViews/TrackListDisplay.astro';
import ArtworkDisplay from '../../../../components/categoryViews/ArtworkDisplay.astro';
import CollapsibleSubmissionForm from '../../../../components/content-submission/CollapsibleSubmissionForm.astro';
import { ENABLE_SUBMISSION_FORM } from '../../../../config/featureFlags';
import type { Track } from '../../../../types/Track';
import type { Artwork } from '../../../../types/Artwork';

// 启用预渲染
export const prerender = true;

// 获取所有分类用于静态生成

export async function getStaticPaths() {
  // 在函数内部定义分类列表，确保与 playboi-carti.astro 中的列表一致
  const cartiCategories = [
    { id: 'art', name: 'Art' },
    { id: 'best-of', name: 'Best Of' },
    { id: 'fakes', name: 'Fakes' },
    { id: 'grails', name: 'Grails' },
    { id: 'groupbuys', name: 'Groupbuys' },
    { id: 'leaks', name: 'Leaks' },
    { id: 'misc', name: 'Misc' },
    { id: 'recent', name: 'Recent' },
    { id: 'released', name: 'Released' },
    { id: 'snippets', name: 'Snippets' },
    { id: 'special', name: 'Special' },
    { id: 'stems', name: 'Stems' },
    { id: 'tracklists', name: 'Tracklists' },
    { id: 'unreleased', name: 'Unreleased' },
    { id: 'worst-of', name: 'Worst Of' }
  ];
  
  const paths = [];

  for (const category of cartiCategories) {
    paths.push({
      params: { categoryId: category.id },
      props: { categoryName: category.name }
    });
  }

  return paths;
}

// 获取特定分类数据
const { categoryId } = Astro.params;
const { categoryName } = Astro.props;
const artistId = 'playboi-carti';
const lang = 'ar'; // 设置语言为阿拉伯语

// 定义分类列表（确保与 playboi-carti.astro 和 getStaticPaths 中的列表一致）
const allCategories = [
  { id: 'art', name: t('categories.art', lang) || 'الفن' },
  { id: 'best-of', name: t('categories.best-of', lang) || 'الأفضل' },
  { id: 'fakes', name: t('categories.fakes', lang) || 'المزيفة' },
  { id: 'grails', name: t('categories.grails', lang) || 'الكنوز' },
  { id: 'groupbuys', name: t('categories.groupbuys', lang) || 'المشتريات الجماعية' },
  { id: 'leaks', name: t('categories.leaks', lang) || 'تسريبات' },
  { id: 'misc', name: t('categories.misc', lang) || 'متنوعات' },
  { id: 'recent', name: t('categories.recent', lang) || 'الأخيرة' },
  { id: 'released', name: t('categories.released', lang) || 'الإصدارات' },
  { id: 'snippets', name: t('categories.snippets', lang) || 'مقتطفات' },
  { id: 'special', name: t('categories.special', lang) || 'خاص' },
  { id: 'stems', name: t('categories.stems', lang) || 'المقاطع' },
  { id: 'tracklists', name: t('categories.tracklists', lang) || 'قوائم المسارات' },
  { id: 'unreleased', name: t('categories.unreleased', lang) || 'غير منشور' },
  { id: 'worst-of', name: t('categories.worst-of', lang) || 'الأسوأ' }
];

// 获取当前分类的翻译名称
const translatedCategoryName = t(`categories.${categoryId}`, lang) || categoryName;

// 面包屑导航数据 - 修正顺序
const breadcrumbs = [
  { name: t('navigation.home', lang) || 'الرئيسية', url: '/ar' },
  { name: t('common.artists', lang) || 'الفنانين', url: '/ar/artists' },
  { name: 'Playboi Carti', url: '/ar/artists/playboi-carti' },
  { name: translatedCategoryName, url: `/ar/artists/playboi-carti/${categoryId}`, current: true }
];

// 加载分类信息和数据
let categoryInfo = null;
let albums = [];
let tracks: Track[] = [];
let artworks: Artwork[] = [];

try {
  // 尝试加载分类信息
  const categoryInfoPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'info.json');

  try {
    const categoryInfoData = await fs.readFile(categoryInfoPath, 'utf-8');
    categoryInfo = JSON.parse(categoryInfoData);
  } catch (infoError) {
    console.error(`未找到分类信息: ${categoryId}:`, infoError);
  }

  // 从info.json中获取显示模式，默认为'albums'
  const displayMode = categoryInfo?.displayMode || 'albums';

  // 只在albums模式下加载eras.json
  if (displayMode === 'albums') {
    try {
      const categoryErasPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras.json');
      const erasData = await fs.readFile(categoryErasPath, 'utf-8');
      albums = JSON.parse(erasData);

      // 确保每个专辑都有coverImage和backgroundColor字段，并检查是否有数据
      albums = await Promise.all(albums.map(async (album) => {
        // 检查该 era 是否有实际的数据文件
        let hasData = false;
        try {
          const eraDataPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras', `${album.id}.json`);
          const eraData = await fs.readFile(eraDataPath, 'utf-8');
          const parsedEraData = JSON.parse(eraData);
          hasData = parsedEraData && parsedEraData.length > 0;
        } catch (error) {
          hasData = false;
        }

        return {
          ...album,
          coverImage: album.coverImage || '/images/eras/default.svg',
          backgroundColor: album.backgroundColor || '#1a1a1a',
          hasData
        };
      }));
    } catch (erasError) {
      console.error(`未找到eras数据: ${categoryId}:`, erasError);
    }
  }
} catch (error) {
  console.error(`加载分类数据时出错: ${categoryId}:`, error);
}

// Get display mode from info.json, default to 'albums'
const displayMode = categoryInfo?.displayMode || 'albums';

// Try to load tracks or artwork data
try {
  const tracksPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'tracks.json');
  const tracksData = await fs.readFile(tracksPath, 'utf-8');
  const parsedData = JSON.parse(tracksData);

  if (displayMode === 'flat') {
    tracks = parsedData;
  } else if (displayMode === 'poster') {
    artworks = parsedData;
  }
} catch (tracksError) {
  // Only log error in flat or poster mode
  if (displayMode === 'flat' || displayMode === 'poster') {
    console.log(`注意: 未找到tracks.json: ${categoryId}, 但这只在flat/poster模式下需要。`);
  }
}

// Check if category has data
const hasData = ['art', 'best-of', 'recent', 'unreleased'].includes(categoryId);

// 根据显示模式选择合适的组件
let ComponentToRender = null;
if (displayMode === 'albums') {
  ComponentToRender = AlbumGridDisplay;
} else if (displayMode === 'flat') {
  ComponentToRender = TrackListDisplay;
} else if (displayMode === 'poster') {
  ComponentToRender = ArtworkDisplay;
} // Add more else if for other display modes

// 构建SEO优化的描述
// 构建优化的 title 和 description
const pageTitle = `${translatedCategoryName} - Playboi Carti - TrackerHive - ${t('common.slogan', lang) || 'مكتبة الموسيقى الرقمية'}`;
const pageDescription = `اكتشف ${translatedCategoryName} من Playboi Carti - ${t(`categories.${categoryId}.description`, lang) || 'مجموعة كاملة من الموسيقى والفن والمحتوى الحصري.'} | ${t('common.siteDescription', lang) || 'أكبر مكتبة للموسيقى غير المنشورة والمحتوى الحصري'}`;

// 构建 canonical URL
const canonicalUrl = Astro.site ? new URL(`/artists/playboi-carti/${categoryId}`, Astro.site).toString() : `/artists/playboi-carti/${categoryId}`;

// 构建结构化数据
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'CollectionPage',
  name: `${translatedCategoryName} - Playboi Carti`,
  description: pageDescription,
  inLanguage: 'ar',
  isPartOf: {
    '@type': 'MusicGroup',
    name: 'Playboi Carti',
    '@id': 'https://aitrackerhive.com/ar/artists/playboi-carti'
  },
  breadcrumb: {
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      item: {
        '@id': `${Astro.url.origin}${item.url}`,
        name: item.name
      }
    }))
  }
};

// 构建Open Graph图片URL
const ogImage = `/images/artists/playboi-carti/${categoryId}-og.jpg`;
---

<MainLayout 
  title={pageTitle}
  description={pageDescription}
  type="music"
  ogImage={ogImage}
  jsonLd={jsonLd}
  canonical={canonicalUrl}
  dir="rtl"
  lang="ar"
>
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
        {breadcrumbs.map((breadcrumb, index) => (
          <li class="inline-flex items-center">
            {index > 0 && (
              <svg 
                class="w-3 h-3 text-gray-400 mx-1" 
                aria-hidden="true" 
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 6 10"
              >
                <path 
                  stroke="currentColor" 
                  stroke-linecap="round" 
                  stroke-linejoin="round" 
                  stroke-width="2" 
                  d="m5 9-4-4 4-4"
                />
              </svg>
            )}
            <a
              href={breadcrumb.url}
              class={`inline-flex items-center text-sm font-medium ${
                breadcrumb.current 
                  ? 'text-primary' 
                  : 'text-gray-400 hover:text-white'
              }`}
              aria-current={breadcrumb.current ? 'page' : undefined}
            >
              {breadcrumb.name}
            </a>
          </li>
        ))}
      </ol>
    </nav>
    

    
    <!-- 所有分类 -->
    <section class="mb-8">
      <h1 class="text-3xl font-bold text-white mb-6 text-right">{translatedCategoryName}</h1>
      <h2 class="text-xl font-bold text-white mb-4 text-right">{t('common.categories', lang) || 'الفئات'}</h2>
      <div class="flex flex-wrap gap-2 justify-end">
        {(() => {
          // 按指定顺序排列有数据的分类
          return allCategories.map(category => {
            const hasData = ['unreleased', 'recent', 'best-of'].includes(category.id);
            const isActive = category.id === categoryId;

            return (
              <a
                href={`/ar/artists/playboi-carti/${category.id}`}
                class={`px-3 py-1.5 rounded-md text-sm transition-all ${
                  isActive
                    ? 'bg-primary text-white font-medium'
                    : hasData
                      ? 'bg-dark-elevated hover:bg-dark-hover text-white hover:text-primary border border-primary/20'
                      : 'bg-dark-elevated hover:bg-dark-hover text-text-secondary hover:text-white opacity-60'
                }`}
              >
                {category.name}
                {hasData && !isActive && <span class="mr-1 text-primary text-xs">●</span>}
              </a>
            );
          });
        })()}
      </div>
    </section>

    <!-- Add Track Section (feature-flagged) -->
    {ENABLE_SUBMISSION_FORM && categoryId !== 'unreleased' && (
      <CollapsibleSubmissionForm artistId={artistId} categoryId={categoryId} />
    )}

    {/* Dynamically render the selected component */}
    {!hasData ? (
      <div class="text-center py-16">
        <div class="max-w-lg mx-auto bg-dark-elevated p-8 rounded-xl border border-dark-hover shadow-lg">
          <div class="flex justify-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-white mb-4 text-center">{t('common.comingSoon', lang) || 'قريبًا'}</h2>
          <p class="text-text-secondary text-center">{t('common.contentBeingCurated', lang) || 'نحن نعمل حاليًا على تنظيم محتوى لفئة'} <span class="text-primary font-semibold">{translatedCategoryName}</span>. {t('common.checkBackSoon', lang) || 'تحقق مرة أخرى قريبًا للحصول على التحديثات!'}</p>
        </div>
      </div>
    ) : ComponentToRender ? (
      displayMode === 'poster' ? (
        <ComponentToRender artworks={artworks} categoryId={categoryId} />
      ) : (
        <ComponentToRender albums={albums} tracks={tracks} categoryId={categoryId} />
      )
    ) : (
      <div class="text-center py-8">
        <p class="text-gray-400">{t('common.displayModeError', lang) || 'خطأ: لم يتم تكوين وضع العرض أو لم يتم العثور على المكون.'}</p>
        <p class="text-gray-500 text-sm mt-2">{t('common.displayModeErrorDetail', lang) || 'تعذر تحديد كيفية عرض المحتوى لهذه الفئة.'}</p>
      </div>
    )}

  </main>
</MainLayout>

<script>
  // 等待DOM加载完成
  document.addEventListener('DOMContentLoaded', () => {
    // 获取所有播放按钮
    const playButtons = document.querySelectorAll('.play-button');
    const audioPlayer = document.querySelector('#audio-player');
    
    if (!audioPlayer) return;
    
    // 为每个按钮添加点击事件
    playButtons.forEach(button => {
      button.addEventListener('click', function() {
        const audioUrl = this.getAttribute('data-audio-url');
        if (!audioUrl) return;
        
        // 重置所有按钮状态
        playButtons.forEach(btn => {
          btn.classList.remove('playing');
          btn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5"><path fill-rule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clip-rule="evenodd" /></svg>';
        });
        
        // 设置当前按钮为播放状态
        this.classList.add('playing');
        this.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5"><path fill-rule="evenodd" d="M6.75 5.25a.75.75 0 01.75-.75H9a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H7.5a.75.75 0 01-.75-.75V5.25zm7.5 0A.75.75 0 0115 4.5h1.5a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H15a.75.75 0 01-.75-.75V5.25z" clip-rule="evenodd" /></svg>';
        
        // 设置音频源并播放
        audioPlayer.setAttribute('src', audioUrl);
        audioPlayer.play();
      });
    });
    
    // 监听音频播放结束事件
    audioPlayer.addEventListener('ended', () => {
      // 重置所有按钮状态
      playButtons.forEach(btn => {
        btn.classList.remove('playing');
        btn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5"><path fill-rule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clip-rule="evenodd" /></svg>';
      });
    });
  });
</script>
