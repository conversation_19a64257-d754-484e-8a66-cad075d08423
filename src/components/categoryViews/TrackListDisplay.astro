---
// src/components/categoryViews/TrackListDisplay.astro
import type { Track } from '../../types/Track'; // Assuming you have a Track type defined
import SmartImage from '../SmartImage.astro';
import TrackDownloadButton from '../TrackDownloadButton.astro';

// 定义 Props
export interface Props {
  tracks: any[]; // 可以是扁平的 Track 数组或者 Era 数组
  categoryId: string;
  albums?: any[]; // 对于 Era 分组的数据
  artistId?: string; // 添加艺术家ID用于条件判断
}

const { tracks, categoryId, albums, artistId } = Astro.props;

// 检查是否需要使用外部链接模式（仅对 Ye 的特定分类）
const useExternalLinks = artistId === 'ye' && ['unreleased', 'recent', 'best-of'].includes(categoryId);

// 检查 tracks 是否是 Era 数组（每个 Era 包含 tracks 数组）
const hasEraStructure = tracks.length > 0 && tracks[0].hasOwnProperty('tracks') && Array.isArray(tracks[0].tracks);

// 质量标签 → 使用自定义 tag 样式类，统一半透明与边框
const qualityColorMap = {
  'High Quality': 'tag tag-green',
  'Low Quality': 'tag tag-red',
  'CD Quality': 'tag tag-purple',
  'Lossless': 'tag tag-purple',
  'Recording': 'tag tag-yellow',
  'Snippet': 'tag tag-blue',
  'Beat Only': 'tag tag-purple',
  'Full': 'tag tag-blue',
  'Tagged': 'tag tag-blue',
  'OG File': 'tag tag-blue'
};

function getQualityClass(quality) {
  return qualityColorMap[quality] || 'tag tag-gray';
}
---

<section>
  {hasEraStructure ? (
    /* Era 结构的数据显示 */
    <div>
      {tracks.length > 0 ? (
        tracks.map((era, eraIndex) => (
          <div class="mb-12">
            <div class="mb-4">
              <h2 class="text-xl font-bold text-white">{era.era}</h2>
            </div>

            <div class="bg-black rounded-xl shadow-md overflow-hidden mb-6">
              {era.tracks && era.tracks.length > 0 ? (
                <div>


                  {/* 轨道列表 */}
                  <div class="divide-y divide-gray-800">
                    {era.tracks.map((track, trackIndex) => (
                      <div class="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-black/40 transition-colors">
                        {/* 轨道信息 */}
                        <div class="col-span-5 md:col-span-6 flex items-start space-x-3">
                          {/* 根据是否有length显示不同的按钮 */}
                          <div class="mr-3 flex items-center">
                            {track.length ? (
                              useExternalLinks ? (
                                <a
                                  href={track.originalUrl || track.url || '#'}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  class="external-link-button w-8 h-8 flex items-center justify-center rounded-full bg-gray-600 text-white hover:bg-gray-700 transition-colors"
                                  title="Open in external player"
                                >
                                  {/* 外部链接图标 */}
                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                  </svg>
                                </a>
                              ) : (
                                <button
                                  class="play-button w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white hover:bg-primary-dark transition-colors"
                                  title="Play"
                                  data-track-name={track.title}
                                  data-track-artists={Array.isArray(track.artists) ? track.artists.join(', ') : (track.artists || '')}
                                  data-track-audio={track.url}
                                  data-track-original-url={track.originalUrl || ''}
                                  data-track-original-content={JSON.stringify(track.originalContent || {})}
                                  data-track-index={trackIndex}
                                >
                                  {/* 播放图标 */}
                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 play-icon" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                  </svg>
                                  {/* 暂停图标 */}
                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 pause-icon hidden" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 011-1h.01a1 1 0 110 2H8a1 1 0 01-1-1zm3 0a1 1 0 011-1h.01a1 1 0 110 2H11a1 1 0 01-1-1z" clip-rule="evenodd" />
                                  </svg>
                                  {/* 加载图标 */}
                                  <svg class="h-4 w-4 loading-icon hidden animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                </button>
                              )
                            ) : track.originalUrl ? (
                              <a href={track.originalUrl} target="_blank" rel="noopener noreferrer" class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors" title="Go to source">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                  <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                              </a>
                            ) : (
                              <div class="w-8 h-8"></div>
                            )}
                          </div>

                          {/* 轨道标题和详情 */}
                          <div class="flex-1 min-w-0 track-content">
                            {track.notes && (
                              <div class="track-notes-tooltip">{track.notes}</div>
                            )}
                            <div class="flex items-center gap-2">
                              <span class="text-white">{track.title}</span>
                              {/* 别名信息 */}
                              {track.aliases && track.aliases.length > 0 ? (
                                <span class="text-yellow-400 text-xs font-medium bg-gray-800/70 px-2 py-0.5 rounded border border-gray-700">
                                  {Array.isArray(track.aliases) ? track.aliases.join(', ') : track.aliases}
                                </span>
                              ) : track.originalContent?.aliases && track.originalContent.aliases.length > 0 ? (
                                <span class="text-yellow-400 text-xs font-medium bg-gray-800/70 px-2 py-0.5 rounded border border-gray-700">
                                  {Array.isArray(track.originalContent.aliases) ? track.originalContent.aliases.join(', ') : track.originalContent.aliases}
                                </span>
                              ) : null}
                            </div>
                            {/* 艺术家信息 - 优先显示外层artists，如果不存在则显示originalContent.artists */}
                            {track.artists ? (
                              <p class="text-text-secondary text-sm mt-1">
                                {Array.isArray(track.artists) ? track.artists.join(', ') : track.artists}
                              </p>
                            ) : track.originalContent?.artists ? (
                              <p class="text-text-secondary text-sm mt-1">
                                {Array.isArray(track.originalContent.artists) 
                                  ? track.originalContent.artists.join(', ') 
                                  : track.originalContent.artists}
                              </p>
                            ) : null}

                            {/* 制作人信息 */}
                            {track.producer && (
                              <div class="text-xs text-gray-500 truncate mt-0.5">
                                (prod. {track.producer})
                              </div>
                            )}


                          </div>
                        </div>

                        {/* 长度 */}
                        <div class="col-span-2 text-right self-center">
                          {track.length && (
                            <span class="text-base text-white font-medium track-length" data-length={track.length}>{track.length}</span>
                          )}
                        </div>

                        {/* 质量/可用性标签 */}
                        <div class="col-span-3 md:col-span-3 text-right self-center flex flex-wrap justify-end gap-1">
                          {/* 质量标签 - 只取第一个值 */}
                          {track.quality && Array.isArray(track.quality) && track.quality.length > 0 ? (
                            <span class={`quality-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(track.quality[0])}`}>
                              {track.quality[0]}
                            </span>
                          ) : track.quality && !Array.isArray(track.quality) ? (
                            <span class={`quality-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(track.quality)}`}>
                              {track.quality}
                            </span>
                          ) : null}

                          {/* 可用性标签 - 只取第一个值 */}
                          {track.available && Array.isArray(track.available) && track.available.length > 0 ? (
                            <span class={`available-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(track.available[0])}`}>
                              {track.available[0]}
                            </span>
                          ) : track.available && !Array.isArray(track.available) ? (
                            <span class={`available-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(track.available)}`}>
                              {track.available}
                            </span>
                          ) : null}

                          {/* 如果有labels字段，也显示为标签 */}
                          {track.labels && track.labels.map((label) => (
                            <span class={`generic-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(label)}`}>
                              {label}
                            </span>
                          ))}


                        </div>

                        {/* 下载按钮 */}
                        <div class="col-span-2 md:col-span-1 text-right self-center">
                          {track.length ? (
                            useExternalLinks ? (
                              <span class="text-gray-500 text-sm">External Link</span>
                            ) : (
                              <TrackDownloadButton
                                track={{
                                  id: track.id || `${era.era}_${trackIndex}`,
                                  title: track.title,
                                  url: track.url,
                                  originalUrl: track.originalUrl,
                                  originalContent: track.originalContent,
                                  size: track.size
                                }}
                                className="track-download"
                              />
                            )
                          ) : (
                            <span class="text-gray-500 text-sm">-</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div class="text-center p-4">
                  <p class="text-gray-400">No tracks found in this era.</p>
                </div>
              )}
            </div>
          </div>
        ))
      ) : (
        <div class="text-center p-8">
          <p class="text-gray-400">No eras found in this category.</p>
          <p class="text-gray-500 text-sm mt-2">There are currently no tracks to display here.</p>
        </div>
      )}
    </div>
  ) : (
    /* 扁平结构的数据显示 */
    <div>
      <h2 class="text-xl font-bold text-white mb-4">Tracks ({tracks.length})</h2>
      <div class="bg-black rounded-xl shadow-md overflow-hidden">
        {tracks && tracks.length > 0 ? (
          <div>


            {/* 轨道列表 */}
            <div class="divide-y divide-gray-800">
              {tracks.map((track, index) => (
                <div class="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-black/40 transition-colors">
                  {/* 轨道信息 */}
                  <div class="col-span-5 md:col-span-6 flex items-start space-x-3">
                    {/* 根据是否有url显示不同的按钮 */}
                    <div class="mr-1 flex items-center">
                      {track.url ? (
                        useExternalLinks ? (
                          <a
                            href={track.originalUrl || track.url || '#'}
                            target="_blank"
                            rel="noopener noreferrer"
                            class="external-link-button w-7 h-7 flex items-center justify-center rounded-full bg-gray-600 text-white hover:bg-gray-700 transition-colors"
                            title="Open in external player"
                          >
                            {/* 外部链接图标 */}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                          </a>
                        ) : (
                          <button
                            class="play-button w-7 h-7 flex items-center justify-center rounded-full bg-primary text-white hover:bg-primary-dark transition-colors"
                            title="Play"
                            data-track-name={track.title}
                            data-track-artists={Array.isArray(track.artists) ? track.artists.join(', ') : (track.artists || '')}
                            data-track-audio={track.url}
                            data-track-original-url={track.originalUrl || ''}
                            data-track-original-content={JSON.stringify(track.originalContent || {})}
                            data-player-id="global-audio-player"
                          >
                            {/* 播放图标 */}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 play-icon" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                            </svg>
                            {/* 暂停图标 */}
                            <svg class="pause-icon hidden h-4 w-4 fill-current text-white" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd"/></svg>
                            {/* 加载图标 */}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 loading-icon hidden animate-spin" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <path d="M12 6v6l4 2"></path>
                            </svg>
                          </button>
                        )
                      ) : track.originalUrl ? (
                        <a href={track.originalUrl} target="_blank" rel="noopener noreferrer" class="w-7 h-7 flex items-center justify-center rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors" title="Go to source">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                          </svg>
                        </a>
                      ) : (
                        <div class="w-7 h-7"></div>
                      )}
                    </div>

                    {/* 轨道标题和详情 */}
                    <div class="flex-1 min-w-0 track-content">
                      {track.notes && (
                        <div class="track-notes-tooltip">{track.notes}</div>
                      )}
                      {/* 标题 - 可能包含高亮部分 */}
                      <div class="flex items-center">
                        {track.titleHighlight ? (
                          <div class="text-white font-medium">
                            {track.title} <span class="inline-block px-2 py-0.5 ml-1 text-xs rounded bg-yellow-600/50 text-yellow-300">{track.titleHighlight}</span>
                          </div>
                        ) : (
                          <div class="text-white font-medium">{track.title}</div>
                        )}
                      </div>

                      {/* 艺术家信息 - 优先显示外层artists，如果不存在则显示originalContent.artists */}
                      {track.artists ? (
                        <div class="text-sm text-gray-400 truncate mt-0.5">
                          {Array.isArray(track.artists) ? track.artists.join(', ') : track.artists}
                        </div>
                      ) : track.originalContent?.artists ? (
                        <div class="text-sm text-gray-400 truncate mt-0.5">
                          {Array.isArray(track.originalContent.artists) 
                            ? track.originalContent.artists.join(', ') 
                            : track.originalContent.artists}
                        </div>
                      ) : (track.artist || track.featuring) && (
                        <div class="text-sm text-gray-400 truncate mt-0.5">
                          {track.artist && <span>{track.artist}</span>}
                          {track.featuring && <span>{track.artist ? ', ' : ''}{track.featuring}</span>}
                        </div>
                      )}

                      {/* 制作人信息 */}
                      {track.producer && (
                        <div class="text-xs text-gray-500 truncate mt-0.5">
                          (prod. {track.producer})
                        </div>
                      )}


                    </div>
                  </div>

                  {/* 长度 */}
                  <div class="col-span-2 text-right self-center">
                    {track.length && (
                      <span class="text-base text-white font-medium track-length" data-length={track.length}>{track.length}</span>
                    )}
                  </div>

                  {/* 质量/可用性标签 */}
                  <div class="col-span-3 md:col-span-3 text-right self-center flex flex-wrap justify-end gap-1">
                    {/* 质量标签 - 只取第一个值 */}
                    {track.quality && Array.isArray(track.quality) && track.quality.length > 0 ? (
                      <span class={`quality-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(track.quality[0])}`}>
                        {track.quality[0]}
                      </span>
                    ) : track.quality && !Array.isArray(track.quality) ? (
                      <span class={`quality-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(track.quality)}`}>
                        {track.quality}
                      </span>
                    ) : null}

                    {/* 可用性标签 - 只取第一个值 */}
                    {track.available && Array.isArray(track.available) && track.available.length > 0 ? (
                      <span class={`available-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(track.available[0])}`}>
                        {track.available[0]}
                      </span>
                    ) : track.available && !Array.isArray(track.available) ? (
                      <span class={`available-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(track.available)}`}>
                        {track.available}
                      </span>
                    ) : null}

                    {/* 如果有labels字段，也显示为标签 */}
                    {track.labels && track.labels.map((label) => (
                      <span class={`generic-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQualityClass(label)}`}>
                        {label}
                      </span>
                    ))}


                  </div>

                  {/* 下载按钮 */}
                  <div class="col-span-2 md:col-span-1 text-right self-center">
                    {track.url ? (
                      useExternalLinks ? (
                        <span class="text-gray-500 text-sm">External Link</span>
                      ) : (
                        <TrackDownloadButton
                          track={{
                            id: track.id || `track_${index}`,
                            title: track.title,
                            url: track.url,
                            originalUrl: track.originalUrl,
                            originalContent: track.originalContent,
                            size: track.size
                          }}
                          className="track-download"
                        />
                      )
                    ) : (
                      <span class="text-gray-500 text-sm">-</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div class="text-center p-8">
            <p class="text-gray-400">No tracks found in this category.</p>
            <p class="text-gray-500 text-sm mt-2">There are currently no tracks to display here.</p>
          </div>
        )}
      </div>
    </div>
  )}
</section>

<style>
  /* Track notes tooltip styles */
  .track-content {
    position: relative;
    display: inline-block;
  }
  
  .track-notes-tooltip {
    display: none;
    position: fixed;
    background-color: #111827;
    color: white;
    padding: 14px 18px;
    border-radius: 10px;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.6;
    white-space: normal;
    max-width: 400px;
    z-index: 1000;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.15);
    pointer-events: none;
    backdrop-filter: blur(5px);
    letter-spacing: 0.01em;
  }
  
  /* Add arrow to tooltip - left side */
  .track-notes-tooltip::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -8px;
    transform: translateY(-50%);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #111827;
  }
  
  /* 标签基本样式 */
  .quality-label,
  .available-label,
  .generic-label {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.625rem;
  }
  /* 通用柔和标签风格 */
  .tag {
    --bg: 55 65 81; /* 默认灰 */
    --fg: 229 231 235;
    color: rgb(var(--fg));
    background-color: rgba(var(--bg) / 0.18); /* 18% 填充 */
    border: 1px solid rgba(var(--bg) / 0.45); /* 45% 边框 */
    box-shadow:
      inset 0 0 0 1px rgba(var(--bg) / 0.12), /* 内边线 */
      0 0 0 1px rgba(var(--bg) / 0.06),       /* 外细线 */
      0 1px 2px rgba(0 0 0 / 0.25);           /* 轻阴影 */
  }
  .tag-green  { --bg: 21 128 61;  --fg: 209 250 229; }
  .tag-blue   { --bg: 29 78 216;  --fg: 191 219 254; }
  .tag-purple { --bg: 126 34 206; --fg: 216 180 254; }
  .tag-yellow { --bg: 161 98 7;   --fg: 253 224 71;  }
  .tag-red    { --bg: 185 28 28;  --fg: 254 202 202; }
  .tag-gray   { --bg: 55 65 81;   --fg: 229 231 235; }
</style>

<script>
  // 处理标签颜色和播放功能
  document.addEventListener('DOMContentLoaded', () => {
    // 处理 notes 提示框跟随鼠标显示
    const trackContents = document.querySelectorAll('.track-content');
    
    trackContents.forEach(content => {
      const tooltip = content.querySelector('.track-notes-tooltip');
      if (tooltip) {
        // 移除媒体查询中的显示逻辑，改为用 JS 控制
        content.addEventListener('mouseenter', () => {
          if (window.innerWidth >= 640) { // 仅在桌面端显示
            tooltip.style.display = 'block';
          }
        });
        
        content.addEventListener('mouseleave', () => {
          tooltip.style.display = 'none';
        });
        
        content.addEventListener('mousemove', (e) => {
          if (window.innerWidth >= 640) { // 仅在桌面端显示
            // 获取鼠标位置
            const x = e.clientX;
            const y = e.clientY;
            
            // 设置提示框位置，在鼠标右侧显示，箭头指向鼠标
            tooltip.style.left = `${x + 15}px`;
            tooltip.style.top = `${y - tooltip.offsetHeight / 2}px`;
          }
        });
      }
    });
    
    // 移除旧的“运行时着色”，改为只添加通用 tag 类，避免覆盖组件映射
    const applyTagLook = (el) => {
      // 清理可能叠加的背景/边框类
      Array.from(el.classList)
        .filter(c => c.startsWith('bg-') || c.startsWith('border-') || c.startsWith('text-') || c.startsWith('bg-opacity-'))
        .forEach(c => el.classList.remove(c));
      // 保留已有的 tag-** 颜色类（由组件层决定），只追加通用外观
      el.classList.add('tag');
    };

    document.querySelectorAll('.quality-label').forEach(applyTagLook);
    document.querySelectorAll('.available-label').forEach(applyTagLook);
    document.querySelectorAll('.generic-label').forEach(applyTagLook);

    
    // 使用底部播放器组件中的音频元素，而不是创建新的
    let globalAudioPlayer = document.getElementById('audio-element');
    if (!globalAudioPlayer) {
      console.warn('Audio element not found, creating a temporary one');
      // 如果没有找到音频元素，创建一个临时的
      globalAudioPlayer = document.createElement('audio');
      globalAudioPlayer.id = 'temp-audio-element';
      document.body.appendChild(globalAudioPlayer);
    }
    
    console.log('Using audio element:', globalAudioPlayer.id);
    
    // 添加音频元素事件监听
    globalAudioPlayer.addEventListener('play', () => {
      console.log('Audio element: play event');
      // 更新当前播放按钮的状态
      if (currentlyPlaying) {
        currentlyPlaying.querySelector('.play-icon')?.classList.add('hidden');
        currentlyPlaying.querySelector('.pause-icon')?.classList.remove('hidden');
        console.log('Updated currentlyPlaying button on play event');
      }
    });
    
    globalAudioPlayer.addEventListener('pause', () => {
      console.log('Audio element: pause event');
      // 确保暂停时更新所有UI
      if (currentlyPlaying) {
        currentlyPlaying.querySelector('.play-icon')?.classList.remove('hidden');
        currentlyPlaying.querySelector('.pause-icon')?.classList.add('hidden');
        console.log('Updated currentlyPlaying button on pause event');
      }
    });
    
    globalAudioPlayer.addEventListener('ended', () => {
      console.log('Audio element: ended event');
      // 播放结束时重置UI
      if (currentlyPlaying) {
        currentlyPlaying.querySelector('.pause-icon').classList.add('hidden');
        currentlyPlaying.querySelector('.play-icon').classList.remove('hidden');
        currentlyPlaying = null;
      }
    });
    
    globalAudioPlayer.addEventListener('error', (e) => {
      console.error('Audio element: error event', e);
      // 错误时重置UI
      if (currentlyPlaying) {
        currentlyPlaying.querySelector('.loading-icon')?.classList.add('hidden');
        currentlyPlaying.querySelector('.play-icon')?.classList.remove('hidden');
        currentlyPlaying.querySelector('.pause-icon')?.classList.add('hidden');
      }
    });
    
    // 我们不需要添加底部播放器的点击事件，因为它已经在AudioPlayer.astro中实现了
    // 监听音频播放器的操作事件
    document.addEventListener('audio-player-action', function(event) {
      console.log('Received audio-player-action:', event.detail.action);
      const { action } = event.detail;
      
      if (action === 'play' && currentlyPlaying) {
        // 更新当前播放按钮的状态
        currentlyPlaying.querySelector('.play-icon')?.classList.add('hidden');
        currentlyPlaying.querySelector('.pause-icon')?.classList.remove('hidden');
        console.log('Updated play button to show pause icon');
      } else if (action === 'pause' && currentlyPlaying) {
        // 更新当前播放按钮的状态
        currentlyPlaying.querySelector('.play-icon')?.classList.remove('hidden');
        currentlyPlaying.querySelector('.pause-icon')?.classList.add('hidden');
        console.log('Updated play button to show play icon');
      }
    });

    
    // 处理播放按钮功能
    const playButtons = document.querySelectorAll('.play-button');
    let currentlyPlaying = null;
    
    playButtons.forEach(button => {
      button.addEventListener('click', function() {
        const audioUrl = this.getAttribute('data-track-audio');
        const originalUrl = this.getAttribute('data-track-original-url');
        const trackName = this.getAttribute('data-track-name');
        const trackArtists = this.getAttribute('data-track-artists');
        
        // 如果没有音频URL，尝试跳转到原始链接
        if (!audioUrl && originalUrl) {
          window.open(originalUrl, '_blank');
          return;
        }
        
        // 如果当前按钮已经在播放，暂停它
        if (currentlyPlaying === this) {
          // 检查当前是否正在播放
          const isPaused = globalAudioPlayer.paused;
          console.log('Current audio state:', isPaused ? 'paused' : 'playing');
          
          if (isPaused) {
            // 继续播放
            console.log('Attempting to resume playback');
            globalAudioPlayer.play()
              .then(() => {
                console.log('Resumed playback successfully');
                // UI更新将由audio元素的play事件处理
              })
              .catch(error => {
                console.error('Error resuming playback:', error);
              });
          } else {
            // 暂停播放
            console.log('Attempting to pause playback');
            globalAudioPlayer.pause();
            // UI更新将由audio元素的pause事件处理
          }
          return;
        }
        
        // 重置之前播放的按钮状态
        if (currentlyPlaying) {
          console.log('Resetting previous button state');
          const prevPlayIcon = currentlyPlaying.querySelector('.play-icon');
          const prevPauseIcon = currentlyPlaying.querySelector('.pause-icon');
          const prevLoadingIcon = currentlyPlaying.querySelector('.loading-icon');
          
          if (prevPlayIcon) prevPlayIcon.classList.remove('hidden');
          if (prevPauseIcon) prevPauseIcon.classList.add('hidden');
          if (prevLoadingIcon) prevLoadingIcon.classList.add('hidden');
        }
        
        // 设置当前播放按钮
        currentlyPlaying = this;
        
        // 显示加载图标
        this.querySelector('.play-icon').classList.add('hidden');
        this.querySelector('.loading-icon').classList.remove('hidden');
        
        // 准备底部播放器需要的曲目信息
        const trackInfo = {
          name: trackName,
          artists: trackArtists ? trackArtists.split(', ') : [],
          audioUrl: audioUrl,
          originalUrl: originalUrl
        };
        
        // 立即更新底部播放器UI，不等待音频加载
        document.getElementById('player-title').textContent = trackName || 'Unknown Track';
        document.getElementById('player-artist').textContent = trackArtists || '-';
        document.getElementById('pause-icon')?.classList.remove('hidden');
        document.getElementById('play-icon')?.classList.add('hidden');
        
        // 更新全局播放状态
        if (window.audioPlayerState) {
          window.audioPlayerState.currentTrack = trackInfo;
          window.audioPlayerState.isPlaying = true;
        }
        
        // 使用底部播放器的playTrack函数播放音频
        if (window.playTrack && typeof window.playTrack === 'function') {
          console.log('Using window.playTrack function');
          window.playTrack(trackInfo, [trackInfo], 0);
          
          // 显示加载中图标，等待音频加载
          this.querySelector('.loading-icon').classList.add('hidden');
          this.querySelector('.pause-icon').classList.remove('hidden');
        } else {
          // 如果底部播放器的 playTrack 函数不可用，使用自定义事件
          console.log('Using custom play-track event');
          const playTrackEvent = new CustomEvent('play-track', {
            detail: {
              track: trackInfo,
              playlist: [trackInfo],
              index: 0
            }
          });
          document.dispatchEvent(playTrackEvent);
          
          // 作为备用，直接设置音频源并播放
          globalAudioPlayer.src = audioUrl;
          globalAudioPlayer.play()
            .then(() => {
              console.log('Audio playback started successfully');
              this.querySelector('.loading-icon').classList.add('hidden');
              this.querySelector('.pause-icon').classList.remove('hidden');
            })
            .catch(error => {
              console.error('Error playing audio:', error);
            });
        }
          
        // 触发自定义事件，通知其他组件正在播放新的音频
        const trackPlayEvent = new CustomEvent('track-play', {
          detail: {
            trackName: trackName,
            trackArtists: trackArtists,
            audioUrl: audioUrl
          }
        });
        document.dispatchEvent(trackPlayEvent);
        
        // 设置音频结束事件处理
        const currentButton = this;
        const onTrackEnded = function() {
          currentButton.querySelector('.pause-icon').classList.add('hidden');
          currentButton.querySelector('.play-icon').classList.remove('hidden');
          currentlyPlaying = null;
          
          // 更新底部播放器状态
          if (window.audioPlayerState) {
            window.audioPlayerState.isPlaying = false;
          }
          
          // 更新底部播放器UI
          document.getElementById('pause-icon')?.classList.add('hidden');
          document.getElementById('play-icon')?.classList.remove('hidden');
          
          // 触发自定义事件，通知播放结束
          const trackEndEvent = new CustomEvent('track-end');
          document.dispatchEvent(trackEndEvent);
          
          // 移除事件监听器
          globalAudioPlayer.removeEventListener('ended', onTrackEnded);
        };
        
        // 设置音频错误事件处理
        const onTrackError = function() {
          currentButton.querySelector('.loading-icon')?.classList.add('hidden');
          currentButton.querySelector('.play-icon')?.classList.remove('hidden');
          console.error('Error playing audio:', audioUrl);
          
          // 如果播放失败，尝试跳转到原始链接
          if (originalUrl) {
            window.open(originalUrl, '_blank');
          }
          
          // 触发自定义事件，通知播放错误
          const trackErrorEvent = new CustomEvent('track-error');
          document.dispatchEvent(trackErrorEvent);
          
          // 移除事件监听器
          globalAudioPlayer.removeEventListener('error', onTrackError);
        };
        
        // 添加事件监听器
        globalAudioPlayer.addEventListener('ended', onTrackEnded);
        globalAudioPlayer.addEventListener('error', onTrackError);
      });
    });
    
    // 监听全局播放器事件，以便其他页面可以控制播放
    document.addEventListener('global-player-control', (event) => {
      const { action, trackUrl } = event.detail;
      
      if (action === 'play' && trackUrl) {
        // 如果是播放特定音频
        const matchingButton = Array.from(playButtons).find(btn => 
          btn.getAttribute('data-track-audio') === trackUrl
        );
        
        if (matchingButton) {
          matchingButton.click(); // 触发点击事件
        } else {
          // 如果没找到匹配的按钮，直接播放
          globalAudioPlayer.src = trackUrl;
          globalAudioPlayer.play();
        }
      } else if (action === 'pause') {
        globalAudioPlayer.pause();
        if (currentlyPlaying) {
          currentlyPlaying.querySelector('.play-icon').classList.remove('hidden');
          currentlyPlaying.querySelector('.pause-icon').classList.add('hidden');
        }
      } else if (action === 'stop') {
        globalAudioPlayer.pause();
        globalAudioPlayer.currentTime = 0;
        if (currentlyPlaying) {
          currentlyPlaying.querySelector('.play-icon').classList.remove('hidden');
          currentlyPlaying.querySelector('.pause-icon').classList.add('hidden');
          currentlyPlaying = null;
        }
      }
    });
    
    // 监听底部播放器的播放/暂停/音量按钮点击事件
    document.addEventListener('audio-player-action', (event) => {
      const { action, volume } = event.detail;
      
      if (action === 'play' && currentlyPlaying) {
        // 底部播放器点击了播放，更新当前按钮状态
        currentlyPlaying.querySelector('.play-icon').classList.add('hidden');
        currentlyPlaying.querySelector('.pause-icon').classList.remove('hidden');
      } else if (action === 'pause' && currentlyPlaying) {
        // 底部播放器点击了暂停，更新当前按钮状态
        currentlyPlaying.querySelector('.play-icon').classList.remove('hidden');
        currentlyPlaying.querySelector('.pause-icon').classList.add('hidden');
      } else if (action === 'volume' && globalAudioPlayer) {
        // 底部播放器音量改变，同步音量
        globalAudioPlayer.volume = volume;
      }
    });
    
    // 如果页面加载时已经有正在播放的曲目，同步状态
    if (window.audioPlayerState && window.audioPlayerState.currentTrack) {
      const currentTrackUrl = window.audioPlayerState.currentTrack.audioUrl;
      if (currentTrackUrl) {
        const matchingButton = Array.from(playButtons).find(btn => 
          btn.getAttribute('data-track-audio') === currentTrackUrl
        );
        
        if (matchingButton && window.audioPlayerState.isPlaying) {
          // 更新按钮状态为播放中
          currentlyPlaying = matchingButton;
          matchingButton.querySelector('.play-icon').classList.add('hidden');
          matchingButton.querySelector('.pause-icon').classList.remove('hidden');
        }
      }
    }
  });
</script>

<!-- 下载功能由全局下载管理器处理 -->
