---
import { t, getLanguageFromURL } from '../../i18n/index.js';

// 获取当前语言
let currentLang = getLanguageFromURL(Astro.request.url);

// 检查 URL 参数中的语言设置
const url = new URL(Astro.request.url);
const langParam = url.searchParams.get('lang');
if (langParam && ['en', 'ar', 'pt'].includes(langParam)) {
  currentLang = langParam;
}

const currentYear = new Date().getFullYear();

// 接收视图类型参数
const { view = "responsive" } = Astro.props;
---

<footer class="border-t border-dark-hover bg-black py-8 lg:py-12 w-full" style="padding-bottom: 120px;">
  <!-- 桌面端样式 (lg及以上) -->
  <div class="hidden lg:block max-w-6xl mx-auto px-6 lg:px-8">
    <div class="flex flex-col lg:flex-row justify-between mb-12">
      <div class="mb-12 lg:mb-0 lg:w-1/3">
        <div class="flex items-center mb-6">
          <svg class="w-8 h-8 text-primary mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
            <path d="M6.85046 16C7.17627 16.6667 8.82373 16.6667 9.14954 16L12 10.9282L14.8505 16C15.1763 16.6667 16.8237 16.6667 17.1495 16L20 10.9282" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M9 9C9 9.55228 8.55228 10 8 10C7.44772 10 7 9.55228 7 9C7 8.44772 7.44772 8 8 8C8.55228 8 9 8.44772 9 9Z" fill="currentColor"/>
            <path d="M17 9C17 9.55228 16.5523 10 16 10C15.4477 10 15 9.55228 15 9C15 8.44772 15.4477 8 16 8C16.5523 8 17 8.44772 17 9Z" fill="currentColor"/>
          </svg>
          <h3 class="text-primary font-bold text-xl">AITrackerHive</h3>
        </div>
        <p class="text-text-secondary text-lg mb-8 max-w-lg leading-relaxed">
          {t('common.explore', currentLang)}
        </p>
      </div>

      <div class="grid grid-cols-2 gap-8 lg:gap-10 lg:w-2/3">
        <!-- 导航分组 -->
        <div>
          <h3 class="font-semibold text-white mb-6 text-base uppercase tracking-wider">{t('navigation.title', currentLang)}</h3>
          <ul class="space-y-4">
            <li><a href={currentLang === 'en' ? '/' : `/${currentLang}/`} class="text-text-secondary hover:text-white text-base transition-colors">{t('navigation.home', currentLang)}</a></li>
            <li><a href={currentLang === 'en' ? '/artists' : `/${currentLang}/artists`} class="text-text-secondary hover:text-white text-base transition-colors">{t('navigation.artists', currentLang)}</a></li>
            <li><a href={currentLang === 'en' ? '/artists/ye/unreleased' : `/${currentLang}/artists/ye/unreleased`} class="text-text-secondary hover:text-white text-base transition-colors">{t('navigation.albums', currentLang)}</a></li>
          </ul>
        </div>

        <!-- 资源分组 -->
        <div>
          <h3 class="font-semibold text-white mb-6 text-base uppercase tracking-wider">{t('resources.title', currentLang)}</h3>
          <ul class="space-y-4">
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <a href="mailto:<EMAIL>" class="text-text-secondary hover:text-white text-base transition-colors"><EMAIL></a>
            </li>
          </ul>
        </div>


      </div>
    </div>

    <!-- Tools 外链（PC端，位于版权上方） -->
    <div class="py-4">
      <a href="https://starterpackai.com" target="_blank" rel="noopener noreferrer"
         class="group inline-flex items-center px-4 py-3 rounded-xl border-2 border-primary/30 hover:border-primary/70 bg-gradient-to-r from-primary/5 via-primary/10 to-transparent hover:from-primary/10 hover:via-primary/20 hover:to-primary/5 transition-all duration-300 hover:shadow-lg hover:shadow-primary/20 hover:scale-105 transform">
        <img src="https://starterpackai.com/favicon.svg" alt="Starterpack AI logo" class="w-5 h-5 mr-3 rounded-md shadow-md group-hover:scale-110 transition-transform duration-300" loading="lazy" />
        <span class="text-white group-hover:text-primary text-sm font-semibold tracking-wide">AI OG Image Generator</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4 text-primary/70 group-hover:text-primary group-hover:translate-x-1 transition-all duration-300" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H7a1 1 0 110-2h7.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </a>
    </div>

    <div class="border-t border-dark-hover pt-6">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <p class="text-base text-text-secondary mb-4 md:mb-0">
          {t('footer.copyright', currentLang)}
        </p>
      </div>
    </div>
  </div>

  <!-- 移动端和平板样式 (lg以下) -->
  <div class="block lg:hidden max-w-6xl mx-auto px-6">
    <div class="flex flex-col">
      <div class="mb-8">
        <div class="flex items-center mb-4">
          <svg class="w-7 h-7 text-primary mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
            <path d="M6.85046 16C7.17627 16.6667 8.82373 16.6667 9.14954 16L12 10.9282L14.8505 16C15.1763 16.6667 16.8237 16.6667 17.1495 16L20 10.9282" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M9 9C9 9.55228 8.55228 10 8 10C7.44772 10 7 9.55228 7 9C7 8.44772 7.44772 8 8 8C8.55228 8 9 8.44772 9 9Z" fill="currentColor"/>
            <path d="M17 9C17 9.55228 16.5523 10 16 10C15.4477 10 15 9.55228 15 9C15 8.44772 15.4477 8 16 8C16.5523 8 17 8.44772 17 9Z" fill="currentColor"/>
          </svg>
          <h3 class="text-primary font-bold text-lg">AITrackerHive</h3>
        </div>
        <p class="text-text-secondary text-base mb-6 max-w-md leading-relaxed">
          {t('common.explore', currentLang)}
        </p>
      </div>

      <div class="grid grid-cols-2 gap-6">
        <div>
          <h3 class="font-semibold text-white mb-4 text-sm uppercase tracking-wider">{t('navigation.title', currentLang)}</h3>
          <ul class="space-y-3">
            <li><a href={currentLang === 'en' ? '/' : `/${currentLang}/`} class="text-text-secondary hover:text-white text-sm transition-colors">{t('navigation.home', currentLang)}</a></li>
            <li><a href={currentLang === 'en' ? '/artists' : `/${currentLang}/artists`} class="text-text-secondary hover:text-white text-sm transition-colors">{t('navigation.artists', currentLang)}</a></li>
            <li><a href={currentLang === 'en' ? '/artists/ye/unreleased' : `/${currentLang}/artists/ye/unreleased`} class="text-text-secondary hover:text-white text-sm transition-colors">{t('navigation.albums', currentLang)}</a></li>
          </ul>
        </div>

        <div>
          <h3 class="font-semibold text-white mb-4 text-sm uppercase tracking-wider">{t('resources.title', currentLang)}</h3>
          <ul class="space-y-3">
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <a href="mailto:<EMAIL>" class="text-text-secondary hover:text-white text-sm transition-colors"><EMAIL></a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Tools 外链（位于版权上方） -->
    <div class="py-4">
      <a href="https://starterpackai.com" target="_blank" rel="noopener noreferrer"
         class="group inline-flex items-center px-4 py-3 rounded-xl border-2 border-primary/30 hover:border-primary/70 bg-gradient-to-r from-primary/5 via-primary/10 to-transparent hover:from-primary/10 hover:via-primary/20 hover:to-primary/5 transition-all duration-300 hover:shadow-lg hover:shadow-primary/20 hover:scale-105 transform">
        <img src="https://starterpackai.com/favicon.svg" alt="Starterpack AI logo" class="w-5 h-5 mr-3 rounded-md shadow-md group-hover:scale-110 transition-transform duration-300" loading="lazy" />
        <span class="text-white group-hover:text-primary text-sm font-semibold tracking-wide">AI OG Image Generator</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4 text-primary/70 group-hover:text-primary group-hover:translate-x-1 transition-all duration-300" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H7a1 1 0 110-2h7.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </a>
    </div>

    <div class="border-t border-dark-hover pt-4">
      <p class="text-sm text-text-secondary text-center">
        {t('footer.copyright', currentLang)}
      </p>
    </div>


  </div>
</footer>
