---
// 精选曲目组件 - 显示随机选择的曲目
import { Image } from 'astro:assets';

// 定义 Props 接口
export interface Props {
  title?: string;
}

const { title = "Latest Updates" } = Astro.props;

// 从 JSON 文件中获取数据
import fs from 'fs';
import path from 'path';

let allTracks = [];

try {
  // 使用 path.join 构建绝对路径
  const featuredTracksPath = path.join(process.cwd(), 'public', 'data', 'featured-tracks.json');
  
  // 读取文件内容
  const fileContent = fs.readFileSync(featuredTracksPath, 'utf-8');
  
  // 解析 JSON 数据
  allTracks = JSON.parse(fileContent);
} catch (error) {
  console.error('Error loading featured tracks:', error);
}

// 注意：我们不再在服务器端随机选择曲目
// 而是将所有曲目传递给客户端，由客户端进行随机选择
---

<section class="mt-12 mb-16">
  <div class="flex items-center mb-6">
    <h2 class="text-2xl font-bold text-white">{title}</h2>
  </div>
  
  <div id="featured-tracks-container" class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <!-- 这里的内容将由客户端JavaScript动态生成 -->
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // 获取所有曲目数据
    fetch('/data/featured-tracks.json')
      .then(response => response.json())
      .then(allTracks => {
        // 随机选择4个曲目
        const featuredTracks = getRandomTracks(allTracks, 4);
        
        // 渲染到页面
        renderFeaturedTracks(featuredTracks);

      })
      .catch(error => {
        console.error('Error loading featured tracks:', error);
      });
    
    // 随机选择曲目函数
    function getRandomTracks(tracks, count) {
      const shuffled = [...tracks].sort(() => 0.5 - Math.random());
      return shuffled.slice(0, count);
    }
    
    // 渲染曲目到页面
    function renderFeaturedTracks(tracks) {
      const container = document.getElementById('featured-tracks-container');
      if (!container) return;
      
      container.innerHTML = tracks.map(track => `
        <div class="track-card bg-dark-elevated rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 hover:transform hover:scale-[1.02] group">
          <div class="block">
            <div class="relative aspect-square">
              <img
                src="${track.albumCover || '/images/album-placeholder.svg'}"
                alt="${track.title}"
                class="w-full h-full object-cover"
                loading="lazy"
                decoding="async"
                fetchpriority="low"
                onerror="this.onerror=null; this.src='/images/album-placeholder.svg';"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-90"></div>
              
              <!-- Play 按钮改为外链跳转 - 居中 -->
              <a
                href="${track.originalUrl || track.url || '#'}"
                target="_blank"
                rel="noopener noreferrer"
                class="external-link-button absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-primary/80 hover:bg-primary text-black rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center transition-all duration-200 z-20 backdrop-blur-sm shadow-lg"
                title="Open in external player"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L12.586 11H3a1 1 0 110-2h9.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </a>

              <!-- 取消下载按钮：去除右下角下载入口 -->

            </div>
            <div class="p-2 sm:p-3">
              <h3 class="font-bold text-white text-sm sm:text-lg mb-0.5 line-clamp-1">${track.title}</h3>
              <p class="text-xs sm:text-sm text-gray-200 font-medium mb-0.5 sm:mb-1 line-clamp-1">${track.artists || 'Ye'}</p>
              <p class="text-xs text-gray-400 line-clamp-1 sm:line-clamp-2 sm:block">${track.era}</p>
            </div>
          </div>
        </div>
      `).join('');
    }

  });
</script>
